'use client'

import { useState } from 'react'

export default function DebugSubscriptionPage() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const simulateSubscription = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/debug/simulate-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}),
      })
      
      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({ error: 'Failed to simulate subscription', details: error })
    } finally {
      setLoading(false)
    }
  }

  const checkSubscriptionStatus = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/stripe/subscription-status')
      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({ error: 'Failed to check subscription status', details: error })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8">Debug Subscription</h1>
      
      <div className="space-y-4">
        <button
          onClick={simulateSubscription}
          disabled={loading}
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
        >
          {loading ? 'Processing...' : 'Simulate Pro Subscription'}
        </button>
        
        <button
          onClick={checkSubscriptionStatus}
          disabled={loading}
          className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50 ml-4"
        >
          {loading ? 'Processing...' : 'Check Subscription Status'}
        </button>
      </div>

      {result && (
        <div className="mt-8">
          <h2 className="text-xl font-semibold mb-4">Result:</h2>
          <pre className="bg-gray-100 p-4 rounded overflow-auto">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  )
}
