import { NextRequest, NextResponse } from 'next/server'

export async function GET(req: NextRequest) {
  try {
    // Make a request to our own checkout session endpoint
    const baseUrl = req.nextUrl.origin
    const priceId = process.env.STRIPE_PRICE_ID_MONTHLY || 'price_1Ro9fpC069EKss8Ct1LEJ9Dg'
    
    console.log('Testing checkout session creation with price ID:', priceId)
    
    const response = await fetch(`${baseUrl}/api/stripe/create-checkout-session`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': req.headers.get('cookie') || '', // Forward cookies for auth
      },
      body: JSON.stringify({
        priceId: priceId,
      }),
    })

    const responseText = await response.text()
    let responseData
    try {
      responseData = JSON.parse(responseText)
    } catch {
      responseData = { rawResponse: responseText }
    }

    return NextResponse.json({
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries()),
      data: responseData,
      priceIdUsed: priceId,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Error testing checkout session:', error)
    return NextResponse.json(
      { 
        error: 'Failed to test checkout session',
        details: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    )
  }
}
