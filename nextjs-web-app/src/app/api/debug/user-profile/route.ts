import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server-client'
import { getUserProfile, updateUserProfile } from '@/lib/database'

export async function GET(req: NextRequest) {
  try {
    // Get the authenticated user
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required', authError },
        { status: 401 }
      )
    }

    console.log('Authenticated user:', {
      id: user.id,
      email: user.email,
      metadata: user.user_metadata
    })

    // Try to get user profile
    const userProfile = await getUserProfile(user.id)
    
    if (!userProfile) {
      console.log('User profile not found, creating one...')
      
      // Create user profile manually
      const { data: newProfile, error: createError } = await supabase
        .from('user_profiles')
        .insert({
          id: user.id,
          email: user.email!,
          full_name: user.user_metadata?.full_name || null,
          avatar_url: user.user_metadata?.avatar_url || null,
          subscription_status: 'free',
          subscription_plan: 'free',
          daily_generations_used: 0,
          daily_generations_reset_date: new Date().toISOString().split('T')[0]
        })
        .select()
        .single()

      if (createError) {
        console.error('Error creating user profile:', createError)
        return NextResponse.json({
          error: 'Failed to create user profile',
          createError,
          user: {
            id: user.id,
            email: user.email,
            metadata: user.user_metadata
          }
        }, { status: 500 })
      }

      return NextResponse.json({
        message: 'User profile created',
        profile: newProfile,
        user: {
          id: user.id,
          email: user.email,
          metadata: user.user_metadata
        }
      })
    }

    return NextResponse.json({
      message: 'User profile found',
      profile: userProfile,
      user: {
        id: user.id,
        email: user.email,
        metadata: user.user_metadata
      }
    })
  } catch (error) {
    console.error('Error in debug endpoint:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error },
      { status: 500 }
    )
  }
}

export async function POST(req: NextRequest) {
  try {
    // Force create/update user profile
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Upsert user profile
    const { data: profile, error: upsertError } = await supabase
      .from('user_profiles')
      .upsert({
        id: user.id,
        email: user.email!,
        full_name: user.user_metadata?.full_name || null,
        avatar_url: user.user_metadata?.avatar_url || null,
        subscription_status: 'free',
        subscription_plan: 'free',
        daily_generations_used: 0,
        daily_generations_reset_date: new Date().toISOString().split('T')[0]
      }, {
        onConflict: 'id'
      })
      .select()
      .single()

    if (upsertError) {
      console.error('Error upserting user profile:', upsertError)
      return NextResponse.json({
        error: 'Failed to upsert user profile',
        upsertError
      }, { status: 500 })
    }

    return NextResponse.json({
      message: 'User profile upserted successfully',
      profile
    })
  } catch (error) {
    console.error('Error in debug POST endpoint:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error },
      { status: 500 }
    )
  }
}
