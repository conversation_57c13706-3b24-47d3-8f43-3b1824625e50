import { NextRequest, NextResponse } from 'next/server'

export async function GET(req: NextRequest) {
  try {
    // Check environment variables
    const stripeSecretKey = process.env.STRIPE_SECRET_KEY
    const stripePublishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
    const stripePriceId = process.env.STRIPE_PRICE_ID_MONTHLY
    const stripeWebhookSecret = process.env.STRIPE_WEBHOOK_SECRET

    // Check if keys look like real Stripe keys
    const isValidSecretKey = stripeSecretKey?.startsWith('sk_test_') || stripeSecretKey?.startsWith('sk_live_')
    const isValidPublishableKey = stripePublishableKey?.startsWith('pk_test_') || stripePublishableKey?.startsWith('pk_live_')
    const isValidPriceId = stripePriceId?.startsWith('price_')
    const isValidWebhookSecret = stripeWebhookSecret?.startsWith('whsec_')

    return NextResponse.json({
      environment: process.env.NODE_ENV,
      stripeConfig: {
        hasSecretKey: !!stripeSecretKey,
        hasPublishableKey: !!stripePublishableKey,
        hasPriceId: !!stripePriceId,
        hasWebhookSecret: !!stripeWebhookSecret,
        secretKeyValid: isValidSecretKey,
        publishableKeyValid: isValidPublishableKey,
        priceIdValid: isValidPriceId,
        webhookSecretValid: isValidWebhookSecret,
        secretKeyPrefix: stripeSecretKey?.substring(0, 10) + '...',
        publishableKeyPrefix: stripePublishableKey?.substring(0, 10) + '...',
        priceIdPrefix: stripePriceId?.substring(0, 10) + '...',
        webhookSecretPrefix: stripeWebhookSecret?.substring(0, 10) + '...'
      }
    })
  } catch (error) {
    console.error('Error checking Stripe config:', error)
    return NextResponse.json(
      { 
        error: 'Failed to check Stripe configuration',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
