import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server-client'
import { getUserProfile, getUserSubscription } from '@/lib/database'

export async function GET(req: NextRequest) {
  try {
    // Get the authenticated user
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { 
          error: 'Authentication required',
          authError: authError?.message,
          user: null
        },
        { status: 401 }
      )
    }

    // Get user profile and subscription
    const userProfile = await getUserProfile(user.id)
    const subscription = await getUserSubscription(user.id)

    // Also get raw data from database
    const { data: rawProfile, error: rawError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    const { data: rawSubscription, error: rawSubError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', user.id)
      .single()

    return NextResponse.json({
      authenticated_user: {
        id: user.id,
        email: user.email,
        created_at: user.created_at,
      },
      user_profile_from_function: userProfile,
      subscription_from_function: subscription,
      raw_profile_from_db: rawProfile,
      raw_profile_error: rawError?.message,
      raw_subscription_from_db: rawSubscription,
      raw_subscription_error: rawSubError?.message,
    })
  } catch (error) {
    console.error('Error in debug user info:', error)
    return NextResponse.json(
      { 
        error: 'Failed to fetch user info',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
