import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server-client'
import { updateUserProfile, createOrUpdateSubscription } from '@/lib/database'

export async function POST(req: NextRequest) {
  try {
    // Get the authenticated user
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get user profile to get stripe_customer_id
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    if (profileError || !profile) {
      return NextResponse.json(
        { error: 'User profile not found' },
        { status: 404 }
      )
    }

    if (!profile.stripe_customer_id) {
      return NextResponse.json(
        { error: 'No Stripe customer ID found. User needs to go through checkout first.' },
        { status: 400 }
      )
    }

    // Simulate a successful subscription
    const now = new Date()
    const nextMonth = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
    
    const mockSubscriptionId = `sub_test_${Date.now()}`
    const priceId = process.env.STRIPE_PRICE_ID_MONTHLY || 'price_1R0728C069EKss8CgWgcGrKN'

    // Create subscription record
    await createOrUpdateSubscription({
      user_id: user.id,
      stripe_subscription_id: mockSubscriptionId,
      stripe_customer_id: profile.stripe_customer_id,
      stripe_price_id: priceId,
      status: 'active',
      current_period_start: now.toISOString(),
      current_period_end: nextMonth.toISOString(),
      cancel_at_period_end: false,
      canceled_at: undefined,
    })

    // Update user profile to pro
    await updateUserProfile(user.id, {
      subscription_status: 'active',
      subscription_plan: 'pro',
      daily_generations_used: 0,
      daily_generations_reset_date: now.toISOString().split('T')[0],
    })

    return NextResponse.json({
      success: true,
      message: 'Successfully simulated pro subscription upgrade',
      subscription: {
        id: mockSubscriptionId,
        status: 'active',
        plan: 'pro',
        current_period_start: now.toISOString(),
        current_period_end: nextMonth.toISOString(),
      },
      user: {
        id: user.id,
        email: user.email,
        subscription_status: 'active',
        subscription_plan: 'pro',
      }
    })
  } catch (error) {
    console.error('Error simulating subscription:', error)
    return NextResponse.json(
      { 
        error: 'Failed to simulate subscription',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
