'use client'

import { useState } from 'react'

export default function DebugStripePage() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const testCheckoutSession = async () => {
    setLoading(true)
    try {
      console.log('Starting checkout session test...')
      const priceId = process.env.NEXT_PUBLIC_STRIPE_PRICE_ID_MONTHLY || 'price_1Ro9fpC069EKss8Ct1LEJ9Dg'
      console.log('Using price ID:', priceId)

      const response = await fetch('/api/stripe/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priceId: priceId,
        }),
      })

      console.log('Response status:', response.status)
      console.log('Response headers:', Object.fromEntries(response.headers.entries()))

      const data = await response.json()
      console.log('Response data:', data)

      setResult({
        status: response.status,
        data,
        priceIdUsed: priceId
      })
    } catch (error) {
      console.error('Error in checkout session test:', error)
      setResult({
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      })
    } finally {
      setLoading(false)
    }
  }

  const testPortalSession = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/stripe/create-portal-session', {
        method: 'POST',
      })

      const data = await response.json()
      setResult({
        status: response.status,
        data
      })
    } catch (error) {
      setResult({
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Debug Stripe Integration</h1>
      
      <div className="space-y-4">
        <button
          onClick={testCheckoutSession}
          disabled={loading}
          className="bg-blue-500 text-white px-4 py-2 rounded disabled:opacity-50"
        >
          {loading ? 'Testing...' : 'Test Checkout Session'}
        </button>

        <button
          onClick={testPortalSession}
          disabled={loading}
          className="bg-green-500 text-white px-4 py-2 rounded disabled:opacity-50"
        >
          {loading ? 'Testing...' : 'Test Portal Session'}
        </button>

        {result && (
          <div className="mt-4 p-4 bg-gray-100 rounded">
            <h2 className="font-bold mb-2">Result:</h2>
            <pre className="text-sm overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  )
}
